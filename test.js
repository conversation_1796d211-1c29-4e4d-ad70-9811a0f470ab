let vm = new Vue({
  el: '#app',
  data() {
    return {
      // 搜索关键词
      searchTerm: '',

      // 商品列表数据
      products: [
        {
          id: 1,
          name: '新鲜牛油果',
          price: 10,
          image: 'http://iph.href.lu/200x200?text=牛油果&bg=99cc00&fg=ffffff',
          stock: 10
        },
        {
          id: 2,
          name: '有机小番茄',
          price: 15,
          image: 'http://iph.href.lu/200x200?text=小番茄&bg=d32776&fg=ffffff',
          stock: 5
        },
        {
          id: 3,
          name: '进口蓝莓',
          price: 25,
          image: 'http://iph.href.lu/200x200?text=蓝莓&bg=1F3050&fg=ffffff',
          stock: 20
        },
        {
          id: 4,
          name: '红苹果',
          price: 8,
          image: 'http://iph.href.lu/200x200?text=红苹果&bg=a32d2d&fg=ffffff',
          stock: 0
        },
        {
          id: 5,
          name: '本地西瓜',
          price: 30,
          image: 'http://iph.href.lu/200x200?text=西瓜&bg=FF8598&fg=ffffff',
          stock: 8
        },
        {
          id: 6,
          name: '香甜橙子',
          price: 12,
          image: 'http://iph.href.lu/200x200?text=橙子&bg=ff9500&fg=ffffff',
          stock: 15
        }
      ],

      // 购物车数据 - 每项包含 {id, name, price, quantity}
      cartItems: []
    }
  },

  created() { },

  mounted() { },

  methods: {
    // 添加商品到购物车
    addToCart(product) {
      // 检查购物车中是否已有该商品
      const existingItem = this.cartItems.find(item => item.id === product.id);

      if (existingItem) {
        // 如果已存在，数量+1
        existingItem.quantity += 1;
      } else {
        // 如果不存在，添加新项
        this.cartItems.push({
          id: product.id,
          name: product.name,
          price: product.price,
          quantity: 1
        });
      }

      // 可以添加一个简单的反馈
      console.log(`${product.name} 已添加到购物车`);
    },

    // 增加商品数量
    increaseQuantity(productId) {
      const item = this.cartItems.find(item => item.id === productId);
      if (item) {
        item.quantity += 1;
      }
    },

    // 减少商品数量
    decreaseQuantity(productId) {
      const item = this.cartItems.find(item => item.id === productId);
      if (item) {
        if (item.quantity > 1) {
          item.quantity -= 1;
        } else {
          // 如果数量为1再减，则直接移除
          this.removeFromCart(productId);
        }
      }
    },

    // 从购物车中移除商品
    removeFromCart(productId) {
      const index = this.cartItems.findIndex(item => item.id === productId);
      if (index > -1) {
        this.cartItems.splice(index, 1);
      }
    }
  },

  computed: {
    // 根据搜索词过滤商品
    filteredProducts() {
      if (!this.searchTerm) {
        return this.products;
      }

      return this.products.filter(product =>
        product.name.toLowerCase().includes(this.searchTerm.toLowerCase())
      );
    },

    // 计算购物车总价
    totalPrice() {
      return this.cartItems.reduce((total, item) => {
        return total + (item.price * item.quantity);
      }, 0);
    },

    // 计算购物车商品总数量
    totalQuantity() {
      return this.cartItems.reduce((total, item) => {
        return total + item.quantity;
      }, 0);
    }
  },

  watch: {}
})
