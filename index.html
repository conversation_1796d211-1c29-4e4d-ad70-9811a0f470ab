<!DOCTYPE html>
<html lang="zh-CN">

<head>
  <meta charset="UTF-8">
  <meta http-equiv="X-UA-Compatible" content="IE=edge">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <script src="https://fastly.jsdelivr.net/npm/@unocss/runtime"></script>
  <script src="https://fastly.jsdelivr.net/npm/vue@2"></script>
  <title>水果店</title>
  <style>
    *,
    *::before,
    *::after {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
    }

    /* 自定义样式补充 */
    .hover-shadow:hover {
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
    }

    .search-input:focus {
      outline: none;
      box-shadow: 0 0 0 3px rgba(77, 186, 135, 0.1);
    }

    button {
      outline: none;
      border: none;
      cursor: pointer;
      user-select: none;
    }
  </style>
</head>

<body class="min-h-screen bg-gray-50">
  <div id="app" class="min-h-screen flex flex-col">

    <!-- Header -->
    <header class="bg-green-500 text-white py-4 px-8 shadow-md">
      <h1 class="text-3xl font-bold text-center">水果店</h1>
    </header>

    <!-- Main Content -->
    <main class="flex-1 flex p-8 gap-8">

      <!-- 左侧：商品列表 -->
      <section class="w-33% min-w-700px bg-white p-6 rounded-lg shadow-sm">
        <div class="mb-6">
          <h2 class="text-2xl font-semibold mb-4 pb-3 border-b-2 border-gray-200">商品列表</h2>

          <!-- 搜索框 -->
          <div class="mb-4">
            <input v-model="searchTerm" type="text" placeholder="搜索商品..."
              class="search-input w-full px-4 py-2 border border-gray-300 rounded-lg focus:border-green-500 transition-all">
          </div>

          <!-- 搜索结果提示 -->
          <p v-if="searchTerm && filteredProducts.length === 0" class="text-gray-500 text-center py-8">
            没有找到相关商品
          </p>

        </div>

        <!-- 商品网格 -->
        <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          <div v-for="product in filteredProducts" :key="product.id"
            class="border border-gray-200 rounded-lg p-4 text-center hover-shadow transition-all duration-300 flex flex-col">
            <img :src="product.image" :alt="product.name" class="w-full h-40 object-cover rounded-md mb-4">
            <div class="flex-grow">
              <h3 class="text-lg font-semibold mb-2">{{ product.name }}</h3>
              <p class="text-xl text-orange-500 font-bold mb-4">¥ {{ product.price.toFixed(2) }}</p>
            </div>

            <!-- 库存判断 -->
            <button v-if="product.stock > 0" @click="addToCart(product)"
              class="bg-green-500 text-white px-4 py-2 rounded-lg hover:bg-green-600 transition-colors w-full">
              加入购物车
            </button>
            <div v-else class="bg-red-100 text-red-700 px-4 py-2 rounded-lg font-bold">
              已售罄
            </div>
          </div>
        </div>
      </section>

      <!-- 右侧：购物车 -->
      <aside class="flex-1 bg-white p-6 rounded-lg shadow-sm flex flex-col">
        <h2 class="text-2xl font-semibold mb-4 pb-3 border-b-2 border-gray-200">
          购物车
          <span v-if="totalQuantity > 0" class="text-lg text-gray-500">({{ totalQuantity }}件)</span>
        </h2>

        <!-- 购物车内容 -->
        <div class="flex-1">
          <!-- 空购物车 -->
          <div v-if="cartItems.length === 0" class="text-center text-gray-500 py-8">
            <p class="text-lg">购物车是空的</p>
            <p class="text-sm mt-2">快去挑选您喜爱的商品吧！</p>
          </div>

          <!-- 购物车商品列表 -->
          <div v-else class="space-y-3">
            <div v-for="item in cartItems" :key="item.id"
              class="flex items-center justify-between py-3 border-b border-gray-100">
              <!-- 商品信息 -->
              <div class="flex-1">
                <h4 class="font-medium text-gray-800">{{ item.name }}</h4>
                <p class="text-sm text-gray-500">¥{{ item.price.toFixed(2) }} × {{ item.quantity }}</p>
              </div>

              <!-- 数量控制 -->
              <div class="flex items-center gap-2 mx-4">
                <button @click="decreaseQuantity(item.id)"
                  class="w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-colors">
                  <span class="text-lg font-bold">-</span>
                </button>
                <span class="w-8 text-center font-semibold">{{ item.quantity }}</span>
                <button @click="increaseQuantity(item.id)"
                  class="w-8 h-8 bg-gray-200 hover:bg-gray-300 rounded-full flex items-center justify-center transition-colors">
                  <span class="text-lg font-bold">+</span>
                </button>
              </div>

              <!-- 小计和删除 -->
              <div class="text-right">
                <p class="font-semibold text-orange-600">¥{{ (item.price * item.quantity).toFixed(2) }}</p>
                <button @click="removeFromCart(item.id)"
                  class="text-red-500 px-3 py-1 rounded-md hover:text-red-700 text-sm mt-1 transition-colors">
                  移除
                </button>
              </div>
            </div>
          </div>
        </div>

        <!-- 购物车总计 -->
        <div v-if="cartItems.length > 0" class="mt-6 pt-4 border-t-2 border-gray-800">
          <div class="flex justify-between items-center text-xl font-bold">
            <span>总计</span>
            <span class="text-orange-600">¥{{ totalPrice.toFixed(2) }}</span>
          </div>
          <button
            class="w-full bg-orange-500 hover:bg-orange-600 text-white py-3 rounded-lg mt-4 font-semibold transition-colors">
            结算 ({{ totalQuantity }}件)
          </button>
        </div>
      </aside>

    </main>

    <!-- Footer -->
    <footer class="bg-gray-800 text-gray-400 py-4 text-center">
      <p>&copy; 2025 水果店</p>
    </footer>
  </div>

  <script src="test.js"></script>
</body>

</html>
